This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2026/dev/Arch Linux) (preloaded format=xelatex 2025.4.1)  21 JUL 2025 13:08
entering extended mode
 \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**main.tex
(./main.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count192
\c@section=\count193
\c@subsection=\count194
\c@subsubsection=\count195
\c@paragraph=\count196
\c@subparagraph=\count197
\c@figure=\count198
\c@table=\count199
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/usr/share/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (/usr/share/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count266
\Gm@cntv=\count267
\c@Gm@tempcnt=\count268
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18
) (/usr/share/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (/usr/share/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
) (/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/usr/share/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks19
\pgfutil@tempdima=\dimen150
\pgfutil@tempdimb=\dimen151
) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box52
) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/share/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/share/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/share/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
)
\Gin@req@height=\dimen152
\Gin@req@width=\dimen153
) (/usr/share/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks20
\pgfkeys@temptoks=\toks21
 (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks22
))
\pgf@x=\dimen154
\pgf@y=\dimen155
\pgf@xa=\dimen156
\pgf@ya=\dimen157
\pgf@xb=\dimen158
\pgf@yb=\dimen159
\pgf@xc=\dimen160
\pgf@yc=\dimen161
\pgf@xd=\dimen162
\pgf@yd=\dimen163
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count269
\c@pgf@countb=\count270
\c@pgf@countc=\count271
\c@pgf@countd=\count272
\t@pgf@toka=\toks23
\t@pgf@tokb=\toks24
\t@pgf@tokc=\toks25
\pgf@sys@id@count=\count273
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-xetex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count274
))) (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count275
\pgfsyssoftpath@bigbuffer@items=\count276
) (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen164
\pgfmath@count=\count277
\pgfmath@box=\box53
\pgfmath@toks=\toks26
\pgfmath@stack@operand=\toks27
\pgfmath@stack@operation=\toks28
) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count278
)) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen165
\pgf@picmaxx=\dimen166
\pgf@picminy=\dimen167
\pgf@picmaxy=\dimen168
\pgf@pathminx=\dimen169
\pgf@pathmaxx=\dimen170
\pgf@pathminy=\dimen171
\pgf@pathmaxy=\dimen172
\pgf@xx=\dimen173
\pgf@xy=\dimen174
\pgf@yx=\dimen175
\pgf@yy=\dimen176
\pgf@zx=\dimen177
\pgf@zy=\dimen178
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen179
\pgf@path@lasty=\dimen180
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen181
\pgf@shorten@start@additional=\dimen182
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box54
\pgf@hbox=\box55
\pgf@layerbox@main=\box56
\pgf@picture@serial@count=\count279
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen183
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen184
\pgf@pt@y=\dimen185
\pgf@pt@temp=\dimen186
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen187
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen188
\pgf@sys@shading@range@num=\count280
\pgf@shadingcount=\count281
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box57
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box58
) (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen189
\pgf@nodesepend=\dimen190
) (/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/usr/share/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/usr/share/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen191
\pgffor@skip=\dimen192
\pgffor@stack=\toks29
\pgffor@toks=\toks30
)) (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count282
\pgfplotmarksize=\dimen193
)
\tikz@lastx=\dimen194
\tikz@lasty=\dimen195
\tikz@lastxsaved=\dimen196
\tikz@lastysaved=\dimen197
\tikz@lastmovetox=\dimen198
\tikz@lastmovetoy=\dimen199
\tikzleveldistance=\dimen256
\tikzsiblingdistance=\dimen257
\tikz@figbox=\box59
\tikz@figbox@bg=\box60
\tikz@tempbox=\box61
\tikz@tempbox@bg=\box62
\tikztreelevel=\count283
\tikznumberofchildren=\count284
\tikznumberofcurrentchild=\count285
\tikz@fig@count=\count286
 (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count287
\pgfmatrixcurrentcolumn=\count288
\pgf@matrix@numberofcolumns=\count289
)
\tikz@expandcount=\count290
 (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks31
\ex@=\dimen258
)) (/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen259
) (/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count291
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count292
\leftroot@=\count293
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count294
\DOTSCASE@=\count295
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box63
\strutbox@=\box64
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen260
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count296
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count297
\dotsspace@=\muskip17
\c@parentequation=\count298
\dspbrk@lvl=\count299
\tag@help=\toks32
\row@=\count300
\column@=\count301
\maxfields@=\count302
\andhelp@=\toks33
\eqnshift@=\dimen261
\alignsep@=\dimen262
\tagshift@=\dimen263
\tagwidth@=\dimen264
\totwidth@=\dimen265
\lineht@=\dimen266
\@envbody=\toks34
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks35
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/share/texmf-dist/tex/latex/mathtools/mathtools.sty
Package: mathtools 2024/10/04 v1.31 mathematical typesetting tools
 (/usr/share/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count303
\calc@Bcount=\count304
\calc@Adimen=\dimen267
\calc@Bdimen=\dimen268
\calc@Askip=\skip54
\calc@Bskip=\skip55
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count305
\calc@Cskip=\skip56
) (/usr/share/texmf-dist/tex/latex/mathtools/mhsetup.sty
Package: mhsetup 2021/03/18 v1.4 programming setup (MH)
)
\g_MT_multlinerow_int=\count306
\l_MT_multwidth_dim=\dimen269
\origjot=\skip57
\l_MT_shortvdotswithinadjustabove_dim=\dimen270
\l_MT_shortvdotswithinadjustbelow_dim=\dimen271
\l_MT_above_intertext_sep=\dimen272
\l_MT_below_intertext_sep=\dimen273
\l_MT_above_shortintertext_sep=\dimen274
\l_MT_below_shortintertext_sep=\dimen275
\xmathstrut@box=\box65
\xmathstrut@dim=\dimen276
) (/usr/share/texmf-dist/tex/latex/tools/bm.sty
Package: bm 2023/12/19 v1.2f Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup4
\symboldletters=\mathgroup5
\symboldsymbols=\mathgroup6
Package bm Info: No bold for \OMX/cmex/m/n, using \pmb.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
) (/usr/share/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box66
\beforetitleunit=\skip58
\aftertitleunit=\skip59
\ttl@plus=\dimen277
\ttl@minus=\dimen278
\ttl@toksa=\toks36
\titlewidth=\dimen279
\titlewidthlast=\dimen280
\titlewidthfirst=\dimen281
) (/usr/share/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
 (/usr/share/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (/usr/share/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen282
\captionmargin=\dimen283
\caption@leftmargin=\dimen284
\caption@rightmargin=\dimen285
\caption@width=\dimen286
\caption@indent=\dimen287
\caption@parindent=\dimen288
\caption@hangindent=\dimen289
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count307
\c@continuedfloat=\count308
)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count309
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count310
) (/usr/share/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX
 (/usr/share/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (/usr/share/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/share/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (/usr/share/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/usr/share/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/share/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (/usr/share/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/share/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (/usr/share/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/share/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (/usr/share/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count311
) (/usr/share/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count312
) (/usr/share/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen290
\Hy@linkcounter=\count313
\Hy@pagecounter=\count314
 (/usr/share/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
) (/usr/share/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count315
 (/usr/share/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count316
 (/usr/share/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen291
 (/usr/share/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/share/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count317
\Field@Width=\dimen292
\Fld@charsize=\dimen293
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (/usr/share/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count318
\c@Item=\count319
\c@Hfootnote=\count320
)
Package hyperref Info: Driver (autodetected): hxetex.
 (/usr/share/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-11-05 v7.01l Hyperref driver for XeTeX
\pdfm@box=\box67
\c@Hy@AnnotLevel=\count321
\HyField@AnnotCount=\count322
\Fld@listcount=\count323
\c@bookmark@seq@number=\count324
 (/usr/share/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (/usr/share/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (/usr/share/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip60
) (/usr/share/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
) (/usr/share/texmf-dist/tex/latex/biblatex/biblatex.sty
Package: biblatex 2024/03/21 v3.20 programmable bibliographies (PK/MW)
 (/usr/share/texmf-dist/tex/latex/logreq/logreq.sty
Package: logreq 2010/08/04 v1.0 xml request logger
\lrq@indent=\count325
 (/usr/share/texmf-dist/tex/latex/logreq/logreq.def
File: logreq.def 2010/08/04 v1.0 logreq spec v1.0
)) (/usr/share/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\c@tabx@nest=\count326
\c@listtotal=\count327
\c@listcount=\count328
\c@liststart=\count329
\c@liststop=\count330
\c@citecount=\count331
\c@citetotal=\count332
\c@multicitecount=\count333
\c@multicitetotal=\count334
\c@instcount=\count335
\c@maxnames=\count336
\c@minnames=\count337
\c@maxitems=\count338
\c@minitems=\count339
\c@citecounter=\count340
\c@maxcitecounter=\count341
\c@savedcitecounter=\count342
\c@uniquelist=\count343
\c@uniquename=\count344
\c@refsection=\count345
\c@refsegment=\count346
\c@maxextratitle=\count347
\c@maxextratitleyear=\count348
\c@maxextraname=\count349
\c@maxextradate=\count350
\c@maxextraalpha=\count351
\c@abbrvpenalty=\count352
\c@highnamepenalty=\count353
\c@lownamepenalty=\count354
\c@maxparens=\count355
\c@parenlevel=\count356
\blx@tempcnta=\count357
\blx@tempcntb=\count358
\blx@tempcntc=\count359
\c@blx@maxsection=\count360
\blx@maxsegment@0=\count361
\blx@notetype=\count362
\blx@parenlevel@text=\count363
\blx@parenlevel@foot=\count364
\blx@sectionciteorder@0=\count365
\blx@sectionciteorderinternal@0=\count366
\blx@entrysetcounter=\count367
\blx@biblioinstance=\count368
\labelnumberwidth=\skip61
\labelalphawidth=\skip62
\biblabelsep=\skip63
\bibitemsep=\skip64
\bibnamesep=\skip65
\bibinitsep=\skip66
\bibparsep=\skip67
\bibhang=\skip68
\blx@bcfin=\read3
\blx@bcfout=\write4
\blx@langwohyphens=\language3
\c@mincomprange=\count369
\c@maxcomprange=\count370
\c@mincompwidth=\count371
Package biblatex Info: Trying to load biblatex default data model...
Package biblatex Info: ... file 'blx-dm.def' found.
 (/usr/share/texmf-dist/tex/latex/biblatex/blx-dm.def
File: blx-dm.def 2024/03/21 v3.20 biblatex datamodel (PK/MW)
)
Package biblatex Info: Trying to load biblatex style data model...
Package biblatex Info: ... file 'ieee.dbx' not found.
Package biblatex Info: Trying to load biblatex custom data model...
Package biblatex Info: ... file 'biblatex-dm.cfg' not found.
\c@afterword=\count372
\c@savedafterword=\count373
\c@annotator=\count374
\c@savedannotator=\count375
\c@author=\count376
\c@savedauthor=\count377
\c@bookauthor=\count378
\c@savedbookauthor=\count379
\c@commentator=\count380
\c@savedcommentator=\count381
\c@editor=\count382
\c@savededitor=\count383
\c@editora=\count384
\c@savededitora=\count385
\c@editorb=\count386
\c@savededitorb=\count387
\c@editorc=\count388
\c@savededitorc=\count389
\c@foreword=\count390
\c@savedforeword=\count391
\c@holder=\count392
\c@savedholder=\count393
\c@introduction=\count394
\c@savedintroduction=\count395
\c@namea=\count396
\c@savednamea=\count397
\c@nameb=\count398
\c@savednameb=\count399
\c@namec=\count400
\c@savednamec=\count401
\c@translator=\count402
\c@savedtranslator=\count403
\c@shortauthor=\count404
\c@savedshortauthor=\count405
\c@shorteditor=\count406
\c@savedshorteditor=\count407
\c@labelname=\count408
\c@savedlabelname=\count409
\c@institution=\count410
\c@savedinstitution=\count411
\c@lista=\count412
\c@savedlista=\count413
\c@listb=\count414
\c@savedlistb=\count415
\c@listc=\count416
\c@savedlistc=\count417
\c@listd=\count418
\c@savedlistd=\count419
\c@liste=\count420
\c@savedliste=\count421
\c@listf=\count422
\c@savedlistf=\count423
\c@location=\count424
\c@savedlocation=\count425
\c@organization=\count426
\c@savedorganization=\count427
\c@origlocation=\count428
\c@savedoriglocation=\count429
\c@origpublisher=\count430
\c@savedorigpublisher=\count431
\c@publisher=\count432
\c@savedpublisher=\count433
\c@language=\count434
\c@savedlanguage=\count435
\c@origlanguage=\count436
\c@savedoriglanguage=\count437
\c@pageref=\count438
\c@savedpageref=\count439
\shorthandwidth=\skip69
\shortjournalwidth=\skip70
\shortserieswidth=\skip71
\shorttitlewidth=\skip72
\shortauthorwidth=\skip73
\shorteditorwidth=\skip74
\locallabelnumberwidth=\skip75
\locallabelalphawidth=\skip76
\localshorthandwidth=\skip77
\localshortjournalwidth=\skip78
\localshortserieswidth=\skip79
\localshorttitlewidth=\skip80
\localshortauthorwidth=\skip81
\localshorteditorwidth=\skip82
Package biblatex Info: Trying to load enhanced support for Unicode engines...
Package biblatex Info: ... file 'blx-unicode.def' found.
 (/usr/share/texmf-dist/tex/latex/biblatex/blx-unicode.def)
Package biblatex Info: Trying to load compatibility code...
Package biblatex Info: ... file 'blx-compat.def' found.
 (/usr/share/texmf-dist/tex/latex/biblatex/blx-compat.def
File: blx-compat.def 2024/03/21 v3.20 biblatex compatibility (PK/MW)
)
Package biblatex Info: Trying to load BibTeX backend compatibility...
Package biblatex Info: ... file 'blx-bibtex.def' found.
 (/usr/share/texmf-dist/tex/latex/biblatex/blx-bibtex.def
File: blx-bibtex.def 2024/03/21 v3.20 biblatex compatibility (PK/MW)


Package biblatex Warning: Using fall-back bibtex backend:
(biblatex)                functionality may be reduced/unavailable.

)
Package biblatex Info: Trying to load generic definitions...
Package biblatex Info: ... file 'biblatex.def' found.
 (/usr/share/texmf-dist/tex/latex/biblatex/biblatex.def
File: biblatex.def 2024/03/21 v3.20 biblatex compatibility (PK/MW)
\c@textcitecount=\count440
\c@textcitetotal=\count441
\c@textcitemaxnames=\count442
\c@biburlbigbreakpenalty=\count443
\c@biburlbreakpenalty=\count444
\c@biburlnumpenalty=\count445
\c@biburlucpenalty=\count446
\c@biburllcpenalty=\count447
\biburlbigskip=\muskip19
\biburlnumskip=\muskip20
\biburlucskip=\muskip21
\biburllcskip=\muskip22
\c@smartand=\count448
)
Package biblatex Info: Trying to load bibliography style 'ieee'...
Package biblatex Info: ... file 'ieee.bbx' found.
 (/usr/share/texmf-dist/tex/latex/biblatex-ieee/ieee.bbx
File: ieee.bbx 2025-01-15 v1.4c biblatex bibliography style
Package biblatex Info: Trying to load bibliography style 'numeric-comp'...
Package biblatex Info: ... file 'numeric-comp.bbx' found.
 (/usr/share/texmf-dist/tex/latex/biblatex/bbx/numeric-comp.bbx
File: numeric-comp.bbx 2024/03/21 v3.20 biblatex bibliography style (PK/MW)
Package biblatex Info: Trying to load bibliography style 'numeric'...
Package biblatex Info: ... file 'numeric.bbx' found.
 (/usr/share/texmf-dist/tex/latex/biblatex/bbx/numeric.bbx
File: numeric.bbx 2024/03/21 v3.20 biblatex bibliography style (PK/MW)
Package biblatex Info: Trying to load bibliography style 'standard'...
Package biblatex Info: ... file 'standard.bbx' found.
 (/usr/share/texmf-dist/tex/latex/biblatex/bbx/standard.bbx
File: standard.bbx 2024/03/21 v3.20 biblatex bibliography style (PK/MW)
\c@bbx:relatedcount=\count449
\c@bbx:relatedtotal=\count450
))))
Package biblatex Info: Trying to load citation style 'ieee'...
Package biblatex Info: ... file 'ieee.cbx' found.
 (/usr/share/texmf-dist/tex/latex/biblatex-ieee/ieee.cbx
File: ieee.cbx 2025-01-15 v1.4c biblatex citation style
Package biblatex Info: Trying to load citation style 'numeric-verb'...
Package biblatex Info: ... file 'numeric-verb.cbx' found.
 (/usr/share/texmf-dist/tex/latex/biblatex/cbx/numeric-verb.cbx
File: numeric-verb.cbx 2024/03/21 v3.20 biblatex citation style (PK/MW)
Package biblatex Info: Redefining '\cite'.
Package biblatex Info: Redefining '\parencite'.
Package biblatex Info: Redefining '\footcite'.
Package biblatex Info: Redefining '\footcitetext'.
Package biblatex Info: Redefining '\smartcite'.
Package biblatex Info: Redefining '\textcite'.
Package biblatex Info: Redefining '\supercite'.
Package biblatex Info: Redefining '\parencites'.
Package biblatex Info: Redefining '\smartcites'.
Package biblatex Info: Redefining '\textcites'.
))
Package biblatex Info: Trying to load configuration file...
Package biblatex Info: ... file 'biblatex.cfg' found.
 (/usr/share/texmf-dist/tex/latex/biblatex/biblatex.cfg
File: biblatex.cfg 
)
Package biblatex Info: XeTeX detected.
(biblatex)             Assuming input encoding 'utf8'.
Package biblatex Info: Document encoding is UTF8 ....
 (/usr/share/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (/usr/share/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count451
\l__pdf_internal_box=\box68
\g__pdf_backend_annotation_int=\count452
\g__pdf_backend_link_int=\count453
))
Package biblatex Info: ... and expl3
(biblatex)             2025-01-18 L3 programming layer (loader) 
(biblatex)             is new enough (at least 2020/04/06),
(biblatex)             setting 'casechanger=expl3'.
 (/usr/share/texmf-dist/tex/latex/biblatex/blx-case-expl3.sty (/usr/share/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: blx-case-expl3 2024/03/21 v3.20 expl3 case changing code for biblatex
)) (/usr/share/texmf-dist/tex/latex/fontspec/fontspec.sty
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
 (/usr/share/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count454
\l__fontspec_language_int=\count455
\l__fontspec_strnum_int=\count456
\l__fontspec_tmp_int=\count457
\l__fontspec_tmpa_int=\count458
\l__fontspec_tmpb_int=\count459
\l__fontspec_tmpc_int=\count460
\l__fontspec_em_int=\count461
\l__fontspec_emdef_int=\count462
\l__fontspec_strong_int=\count463
\l__fontspec_strongdef_int=\count464
\l__fontspec_tmpa_dim=\dimen294
\l__fontspec_tmpb_dim=\dimen295
\l__fontspec_tmpc_dim=\dimen296
 (/usr/share/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (/usr/share/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (/usr/share/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers
\f@nch@headwidth=\skip83
\f@nch@offset@elh=\skip84
\f@nch@offset@erh=\skip85
\f@nch@offset@olh=\skip86
\f@nch@offset@orh=\skip87
\f@nch@offset@elf=\skip88
\f@nch@offset@erf=\skip89
\f@nch@offset@olf=\skip90
\f@nch@offset@orf=\skip91
\f@nch@height=\skip92
\f@nch@footalignment=\skip93
\f@nch@widthL=\skip94
\f@nch@widthC=\skip95
\f@nch@widthR=\skip96
\@temptokenb=\toks37
) (/usr/share/texmf-dist/tex/latex/listings/listings.sty
\lst@mode=\count465
\lst@gtempboxa=\box69
\lst@token=\toks38
\lst@length=\count466
\lst@currlwidth=\dimen297
\lst@column=\count467
\lst@pos=\count468
\lst@lostspace=\dimen298
\lst@width=\dimen299
\lst@newlines=\count469
\lst@lineno=\count470
\lst@maxwidth=\dimen300
 (/usr/share/texmf-dist/tex/latex/listings/lstpatch.sty
File: lstpatch.sty 2024/09/23 1.10c (Carsten Heinz)
) (/usr/share/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2024/09/23 1.10c (Carsten Heinz)
\c@lstnumber=\count471
\lst@skipnumbers=\count472
\lst@framebox=\box70
) (/usr/share/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2024/09/23 1.10c listings configuration
))
Package: listings 2024/09/23 1.10c (Carsten Heinz)
 (/usr/share/texmf-dist/tex/latex/comment/comment.sty
\CommentStream=\write5
 Excluding comment 'comment') (/usr/share/texmf-dist/tex/latex/stackengine/stackengine.sty
Package: stackengine 2021/07/22 v4.11\ Stacking text and objects in convenient ways
 (/usr/share/texmf-dist/tex/generic/listofitems/listofitems.sty (/usr/share/texmf-dist/tex/generic/listofitems/listofitems.tex
\loi_cnt_foreach_nest=\count473
\loi_nestcnt=\count474
)
Package: listofitems 2024/03/09 v1.65 Grab items in lists using user-specified sep char (CT)
)
\c@@stackindex=\count475
\@boxshift=\skip97
\stack@tmplength=\skip98
\temp@stkl=\skip99
\@stackedboxwidth=\skip100
\@addedbox=\box71
\@anchorbox=\box72
\@insetbox=\box73
\se@backgroundbox=\box74
\stackedbox=\box75
\@centerbox=\box76
\c@ROWcellindex@=\count476
) (/usr/share/texmf-dist/tex/latex/psnfss/times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
) (/usr/share/texmf-dist/tex/latex/cancel/cancel.sty
Package: cancel 2013/04/12 v2.2 Cancel math terms
) (/usr/share/texmf-dist/tex/latex/changepage/changepage.sty
Package: changepage 2009/10/20 v1.0c check page and change page layout
\c@cp@cntr=\count477
\cp@tempcnt=\count478
) (/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/usr/share/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/share/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup7
\symAMSb=\mathgroup8
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
\c@theorem=\count479
\c@axiom=\count480

Package fontspec Info: 
(fontspec)             Font family 'TimesNewRoman(0)' created for font 'Times
(fontspec)             New Roman' with options [Ligatures=TeX].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"Times New
(fontspec)             Roman/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"Times New
(fontspec)             Roman/B/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.: <->"Times New
(fontspec)             Roman/I/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"Times New
(fontspec)             Roman/BI/OT:script=latn;language=dflt;mapping=tex-text;"

\@quotelevel=\count481
\@quotereset=\count482
 (./main.aux)
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 108.
LaTeX Font Info:    ... okay on input line 108.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 108.
LaTeX Font Info:    ... okay on input line 108.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 108.
LaTeX Font Info:    ... okay on input line 108.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 108.
LaTeX Font Info:    ... okay on input line 108.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 108.
LaTeX Font Info:    ... okay on input line 108.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 108.
LaTeX Font Info:    ... okay on input line 108.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 108.
LaTeX Font Info:    ... okay on input line 108.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 108.
LaTeX Font Info:    ... okay on input line 108.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 108.
LaTeX Font Info:    ... okay on input line 108.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 108.
LaTeX Font Info:    ... okay on input line 108.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package caption Info: Begin \AtBeginDocument code.
Package caption Info: changepage package is loaded.
\caption@adjustwidth@hsize=\dimen301
\caption@adjustwidth@linewidth=\dimen302
Package caption Info: hyperref package is loaded.
Package caption Info: listings package is loaded.
Package caption Info: End \AtBeginDocument code.
Package hyperref Info: Link coloring OFF on input line 108.
(./main.out) (./main.out)
\@outlinefile=\write6
\openout6 = `main.out'.

Package biblatex Info: Trying to load language 'english'...
Package biblatex Info: ... file 'english.lbx' found.
 (/usr/share/texmf-dist/tex/latex/biblatex/lbx/english.lbx
File: english.lbx 2024/03/21 v3.20 biblatex localization (PK/MW)
)
Package biblatex Info: XeTeX detected.
(biblatex)             Assuming input encoding 'utf8'.
Package biblatex Info: Automatic encoding selection.
(biblatex)             Assuming data encoding 'utf8'.
Package biblatex Info: XeTeX detected.
(biblatex)             Assuming input encoding 'utf8'.
Package biblatex Info: Data encoding 'utf8' specified.
(biblatex)             No need to reencode data.
\openout4 = `main-blx.bib'.

Package biblatex Info: Trying to load bibliographic data...
Package biblatex Info: ... file 'main.bbl' not found.

No file main.bbl.
Package biblatex Info: Reference section=0 on input line 108.
Package biblatex Info: Reference segment=0 on input line 108.

Package fontspec Info: 
(fontspec)             Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup9
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 108.
LaTeX Font Info:    Redeclaring math accent \acute on input line 108.
LaTeX Font Info:    Redeclaring math accent \grave on input line 108.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 108.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 108.
LaTeX Font Info:    Redeclaring math accent \bar on input line 108.
LaTeX Font Info:    Redeclaring math accent \breve on input line 108.
LaTeX Font Info:    Redeclaring math accent \check on input line 108.
LaTeX Font Info:    Redeclaring math accent \hat on input line 108.
LaTeX Font Info:    Redeclaring math accent \dot on input line 108.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 108.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 108.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 108.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 108.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 108.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 108.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 108.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 108.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 108.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 108.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 108.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 108.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 108.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 108.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 108.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/TimesNewRoman(0)/m/n on input line 108.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 108.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRoman(0)/m/n on input line 108.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/TimesNewRoman(0)/m/n --> TU/TimesNewRoman(0)/m/n on input line 108.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/TimesNewRoman(0)/m/it on input line 108.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/TimesNewRoman(0)/b/n on input line 108.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/phv/m/n on input line 108.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/pcr/m/n on input line 108.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/TimesNewRoman(0)/m/n --> TU/TimesNewRoman(0)/b/n on input line 108.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/TimesNewRoman(0)/b/it on input line 108.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/phv/b/n on input line 108.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/pcr/b/n on input line 108.
\c@lstlisting=\count483
(./title.tex
LaTeX Font Info:    Trying to load font information for U+msa on input line 8.
 (/usr/share/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 8.
 (/usr/share/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[1

]) (./abstract.tex)

[1] (./acknowledgments.tex)

[2] (./main.toc

[3])
\tf@toc=\write7
\openout7 = `main.toc'.



[4] (./main.lof)
\tf@lof=\write8
\openout8 = `main.lof'.



[5] (./Chapters/Intro.tex

LaTeX Warning: Citation 'Dirac1928' on page 1 undefined on input line 16.



[1]

[2]

LaTeX Warning: Citation 'Armitage2018' on page 3 undefined on input line 55.



[3]

[4]

[5]

[6]

LaTeX Warning: Citation 'utermohlen2018graphene' on page 7 undefined on input line 184.


Overfull \hbox (0.79381pt too wide) in paragraph at lines 183--186
\TU/TimesNewRoman(0)/m/n/12 The derivation of this Hamiltonian is based on the derivation of the tight-binding model for graphene
 []



[7] Excluding 'comment' comment.
File: Chapters/DiracCone.png Graphic file (type bmp)
<Chapters/DiracCone.png>


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 247.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `subscript' on input line 247.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 247.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 248.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `subscript' on input line 248.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 248.


LaTeX Warning: Citation 'Pelayo2024' on page 8 undefined on input line 250.

File: Chapters/PtTe2.png Graphic file (type bmp)
<Chapters/PtTe2.png>

LaTeX Warning: Citation 'Yan2017' on page 8 undefined on input line 256.


LaTeX Warning: Citation 'Yan2017' on page 8 undefined on input line 256.



[8]

Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 258.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `subscript' on input line 258.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 258.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 258.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `subscript' on input line 258.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 258.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 258.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `subscript' on input line 258.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `math shift' on input line 258.



[9]

[10]
File: Chapters/PtTe2Fermi2.png Graphic file (type bmp)
<Chapters/PtTe2Fermi2.png>


[11])

[12] (./Chapters/Walecka.tex

[13

]

[14]

[15]
File: Chapters/ARPESDiagram.png Graphic file (type bmp)
<Chapters/ARPESDiagram.png>
File: Chapters/BeamlineImage.jpg Graphic file (type bmp)
<Chapters/BeamlineImage.jpg>


[16]

[17]
File: Chapters/ARPESSuite.png Graphic file (type bmp)
<Chapters/ARPESSuite.png>


[18]

[19]
File: Chapters/MovingAverageExample.png Graphic file (type bmp)
<Chapters/MovingAverageExample.png>


[20]

[21]

[22]
Overfull \hbox (5.27129pt too wide) detected at line 172
\OML/cmm/m/it/12 I \OMS/cmsy/m/n/12 7! \OML/cmm/m/it/12 N [] \OT1/cmr/m/n/12 = []
 []

File: Chapters/CannyEdge.png Graphic file (type bmp)
<Chapters/CannyEdge.png>
)

[23]

[24] (./Chapters/DensityFunctionalApproach.tex

LaTeX Warning: Citation 'Nakahara:2003' on page 25 undefined on input line 19.



[25

]

[26]
File: Chapters/DoughnutMug.png Graphic file (type bmp)
<Chapters/DoughnutMug.png>


LaTeX Warning: Citation 'Dana2022' on page 27 undefined on input line 44.


LaTeX Warning: Citation 'Dana2022' on page 27 undefined on input line 44.


LaTeX Warning: Citation 'Nakahara:2003' on page 27 undefined on input line 46.



[27]

[28]

[29]

LaTeX Warning: Citation 'Nakahara:2003' on page 30 undefined on input line 82.



[30]
Overfull \hbox (35.02142pt too wide) in paragraph at lines 89--91
\TU/TimesNewRoman(0)/m/n/12 We can obtain coordinate representations of the points by using our charts $\OML/cmm/m/it/12 '\OT1/cmr/m/n/12 (\OML/cmm/m/it/12 x\OT1/cmr/m/n/12 ) = [] = []$
 []


LaTeX Warning: Citation 'Nakahara:2003' on page 31 undefined on input line 94.


LaTeX Warning: Citation 'Nakahara:2003' on page 31 undefined on input line 99.



[31]

LaTeX Warning: Citation 'nash1988' on page 32 undefined on input line 116.



[32]

[33]

LaTeX Warning: Citation 'fushidahardymorse' on page 34 undefined on input line 136.



[34]

[35]

[36]
File: Chapters/CrSubSeries.png Graphic file (type bmp)
<Chapters/CrSubSeries.png>
File: Chapters/EDPlot.png Graphic file (type bmp)
<Chapters/EDPlot.png>


[37]

[38]

[39]
Overfull \hbox (20.63618pt too wide) detected at line 210
[][][]\OML/cmm/m/it/12 T \OT1/cmr/m/n/12 : \OMS/cmsy/m/n/12 D 7! D[]\OML/cmm/m/it/12 ; [] \OMS/cmsy/m/n/12 7! []
 []



[40]

[41]
File: Chapters/CriticalPointsEkx-2.png Graphic file (type bmp)
<Chapters/CriticalPointsEkx-2.png>
File: Chapters/CriticalPointsEkyNoSpike.png Graphic file (type bmp)
<Chapters/CriticalPointsEkyNoSpike.png>
File: Chapters/CriticalPointsEkySpike.png Graphic file (type bmp)
<Chapters/CriticalPointsEkySpike.png>


[42]

[43]

[44]

[45]

[46])

[47] (./Chapters/Conclusion.tex)
Overfull \hbox (7.74232pt too wide) in paragraph at lines 2--140
\TU/TimesNewRoman(0)/m/n/12 In this study we have outlined our materials of interest PtTe$[]$ and its Cr alloys of the form Cr$[]$Pt$[]$Te$[]$.
 []



[48

]

[49]

LaTeX Warning: Empty bibliography on input line 148.

(./main.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********


LaTeX Warning: There were undefined references.

Package rerunfilecheck Info: File `main.out' has not changed.
(rerunfilecheck)             Checksum: 10826BA0A396ED16C7146E9CA31200FB;8158.

Package biblatex Warning: Please (re)run BibTeX on the file(s):
(biblatex)                main
(biblatex)                and rerun LaTeX afterwards.

Package logreq Info: Writing requests to 'main.run.xml'.
\openout1 = `main.run.xml'.

 ) 
Here is how much of TeX's memory you used:
 35008 strings out of 475848
 696734 string characters out of 5776014
 1347379 words of memory out of 5000000
 57376 multiletter control sequences out of 15000+600000
 573740 words of font info for 116 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 102i,13n,107p,1915b,3740s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on main.xdv (55 pages, 845668 bytes).
